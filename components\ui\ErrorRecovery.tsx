import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import {
  AlertTriangle,
  RefreshCw,
  Wifi,
  Settings,
  HelpCircle,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';

interface ErrorRecoveryProps {
  error:
    | {
        type:
          | 'network'
          | 'validation'
          | 'ai_generation'
          | 'submission'
          | 'authentication';
        message: string;
        retryable: boolean;
        context?: Record<string, any>;
        timestamp: string;
      }
    | string; // Support legacy string errors
  onRetry?: () => void;
  onFallback?: () => void;
  onContactSupport?: () => void;
  showDetails?: boolean;
}

export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onFallback,
  onContactSupport,
  showDetails = false,
}) => {
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Normalize error to structured format
  const normalizedError =
    typeof error === 'string'
      ? {
          type: 'network' as const,
          message: error,
          retryable: true,
          timestamp: new Date().toISOString(),
        }
      : error;

  const getErrorIcon = () => {
    switch (normalizedError.type) {
      case 'network':
        return <Wifi size={48} color="#EF4444" />;
      case 'authentication':
        return <Settings size={48} color="#EF4444" />;
      case 'ai_generation':
        return <RefreshCw size={48} color="#F59E0B" />;
      default:
        return <AlertTriangle size={48} color="#EF4444" />;
    }
  };

  const getErrorTitle = () => {
    switch (normalizedError.type) {
      case 'network':
        return 'Connection Problem';
      case 'authentication':
        return 'Authentication Required';
      case 'ai_generation':
        return 'Question Generation Failed';
      case 'submission':
        return 'Submission Failed';
      case 'validation':
        return 'Invalid Data';
      default:
        return 'Something Went Wrong';
    }
  };

  const getErrorDescription = () => {
    switch (normalizedError.type) {
      case 'network':
        return 'Please check your internet connection and try again.';
      case 'authentication':
        return 'Please sign in to continue with your assessment.';
      case 'ai_generation':
        return 'We can use standard questions instead.';
      case 'submission':
        return 'Your responses are saved. Please try submitting again.';
      case 'validation':
        return 'Please check your responses and try again.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  };

  const getSuggestedActions = () => {
    const actions = [];

    if (normalizedError.retryable && onRetry) {
      actions.push({
        label:
          retryCount > 0 ? 'Retry (' + (retryCount + 1) + ')' : 'Try Again',
        action: () => {
          setRetryCount((prev) => prev + 1);
          onRetry();
        },
        primary: true,
        icon: <RefreshCw size={16} color="#ffffff" />,
      });
    }

    if (normalizedError.type === 'ai_generation' && onFallback) {
      actions.push({
        label: 'Use Standard Questions',
        action: onFallback,
        primary: false,
        icon: <Settings size={16} color="#F59E0B" />,
      });
    }

    if (normalizedError.type === 'network') {
      actions.push({
        label: 'Check Connection',
        action: () => {
          Alert.alert(
            'Network Troubleshooting',
            '• Check your WiFi or mobile data\n• Try moving to a better signal area\n• Restart your internet connection\n• Contact your network provider if issues persist',
            [{ text: 'OK' }]
          );
        },
        primary: false,
        icon: <Wifi size={16} color="#3B82F6" />,
      });
    }

    if (onContactSupport) {
      actions.push({
        label: 'Contact Support',
        action: onContactSupport,
        primary: false,
        icon: <HelpCircle size={16} color="#6B7280" />,
      });
    }

    return actions;
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch {
      return 'Unknown time';
    }
  };

  const actions = getSuggestedActions();

  return (
    <View
      style={styles.container}
      accessible={true}
      accessibilityRole="alert"
      accessibilityLabel={
        'Error: ' + getErrorTitle() + '. ' + normalizedError.message
      }
    >
      <View style={styles.iconContainer}>{getErrorIcon()}</View>

      <Text style={styles.title}>{getErrorTitle()}</Text>
      <Text style={styles.message}>{normalizedError.message}</Text>
      <Text style={styles.description}>{getErrorDescription()}</Text>

      {actions.length > 0 && (
        <View style={styles.actionsContainer}>
          {actions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.actionButton,
                action.primary ? styles.primaryButton : styles.secondaryButton,
              ]}
              onPress={action.action}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel={action.label}
            >
              {action.icon}
              <Text
                style={[
                  styles.actionButtonText,
                  action.primary
                    ? styles.primaryButtonText
                    : styles.secondaryButtonText,
                ]}
              >
                {action.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {showDetails && (
        <View style={styles.detailsContainer}>
          <TouchableOpacity
            style={styles.detailsToggle}
            onPress={() => setIsDetailsExpanded(!isDetailsExpanded)}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel={
              (isDetailsExpanded ? 'Hide' : 'Show') + ' error details'
            }
          >
            <Text style={styles.detailsToggleText}>
              {isDetailsExpanded ? 'Hide Details' : 'Show Details'}
            </Text>
            {isDetailsExpanded ? (
              <ChevronUp size={16} color="#6B7280" />
            ) : (
              <ChevronDown size={16} color="#6B7280" />
            )}
          </TouchableOpacity>

          {isDetailsExpanded && (
            <View style={styles.detailsContent}>
              <Text style={styles.detailsLabel}>Error Type:</Text>
              <Text style={styles.detailsValue}>{normalizedError.type}</Text>

              <Text style={styles.detailsLabel}>Timestamp:</Text>
              <Text style={styles.detailsValue}>
                {formatTimestamp(normalizedError.timestamp)}
              </Text>

              {normalizedError.context && (
                <>
                  <Text style={styles.detailsLabel}>Additional Info:</Text>
                  <Text style={styles.detailsValue}>
                    {JSON.stringify(normalizedError.context, null, 2)}
                  </Text>
                </>
              )}

              <Text style={styles.detailsLabel}>Retryable:</Text>
              <Text style={styles.detailsValue}>
                {normalizedError.retryable ? 'Yes' : 'No'}
              </Text>
            </View>
          )}
        </View>
      )}

      {retryCount > 2 && (
        <View style={styles.persistentErrorNotice}>
          <Text style={styles.persistentErrorText}>
            Still having trouble? This might be a temporary issue. You can try
            again later or contact support for assistance.
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 32,
    backgroundColor: '#ffffff',
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
  },
  actionsContainer: {
    width: '100%',
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#3B82F6',
  },
  secondaryButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  primaryButtonText: {
    color: '#ffffff',
  },
  secondaryButtonText: {
    color: '#374151',
  },
  detailsContainer: {
    width: '100%',
    marginTop: 16,
  },
  detailsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    gap: 4,
  },
  detailsToggleText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  detailsContent: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  detailsLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginTop: 8,
    marginBottom: 2,
  },
  detailsValue: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'monospace',
  },
  persistentErrorNotice: {
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
    width: '100%',
  },
  persistentErrorText: {
    fontSize: 14,
    color: '#92400E',
    textAlign: 'center',
    lineHeight: 20,
  },
});
