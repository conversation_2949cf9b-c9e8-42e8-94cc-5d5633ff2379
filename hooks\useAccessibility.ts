import { useState, useEffect } from 'react';
import { AccessibilityInfo, Appearance } from 'react-native';

interface AccessibilityState {
  isScreenReaderEnabled: boolean;
  isReduceMotionEnabled: boolean;
  isReduceTransparencyEnabled: boolean;
  isBoldTextEnabled: boolean;
  isGrayscaleEnabled: boolean;
  isInvertColorsEnabled: boolean;
  preferredContentSizeCategory: string;
  colorScheme: 'light' | 'dark' | null;
}

export const useAccessibility = () => {
  const [accessibilityState, setAccessibilityState] = useState<AccessibilityState>({
    isScreenReaderEnabled: false,
    isReduceMotionEnabled: false,
    isReduceTransparencyEnabled: false,
    isBoldTextEnabled: false,
    isGrayscaleEnabled: false,
    isInvertColorsEnabled: false,
    preferredContentSizeCategory: 'medium',
    colorScheme: null,
  });

  useEffect(() => {
    const checkAccessibilitySettings = async () => {
      try {
        const [
          screenReader,
          reduceMotion,
          reduceTransparency,
          boldText,
          grayscale,
          invertColors,
          contentSize,
        ] = await Promise.all([
          AccessibilityInfo.isScreenReaderEnabled(),
          AccessibilityInfo.isReduceMotionEnabled(),
          AccessibilityInfo.isReduceTransparencyEnabled(),
          AccessibilityInfo.isBoldTextEnabled(),
          AccessibilityInfo.isGrayscaleEnabled(),
          AccessibilityInfo.isInvertColorsEnabled(),
          AccessibilityInfo.getRecommendedTimeoutMillis(1000),
        ]);

        setAccessibilityState(prev => ({
          ...prev,
          isScreenReaderEnabled: screenReader,
          isReduceMotionEnabled: reduceMotion,
          isReduceTransparencyEnabled: reduceTransparency,
          isBoldTextEnabled: boldText,
          isGrayscaleEnabled: grayscale,
          isInvertColorsEnabled: invertColors,
          colorScheme: Appearance.getColorScheme(),
        }));
      } catch (error) {
        console.warn('Error checking accessibility settings:', error);
      }
    };

    checkAccessibilitySettings();

    // Set up listeners for accessibility changes
    const screenReaderListener = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      (isEnabled) => {
        setAccessibilityState(prev => ({ ...prev, isScreenReaderEnabled: isEnabled }));
      }
    );

    const reduceMotionListener = AccessibilityInfo.addEventListener(
      'reduceMotionChanged',
      (isEnabled) => {
        setAccessibilityState(prev => ({ ...prev, isReduceMotionEnabled: isEnabled }));
      }
    );

    const boldTextListener = AccessibilityInfo.addEventListener(
      'boldTextChanged',
      (isEnabled) => {
        setAccessibilityState(prev => ({ ...prev, isBoldTextEnabled: isEnabled }));
      }
    );

    const colorSchemeListener = Appearance.addChangeListener(({ colorScheme }) => {
      setAccessibilityState(prev => ({ ...prev, colorScheme }));
    });

    return () => {
      screenReaderListener?.remove();
      reduceMotionListener?.remove();
      boldTextListener?.remove();
      colorSchemeListener?.remove();
    };
  }, []);

  // Helper functions
  const announceForScreenReader = (message: string) => {
    if (accessibilityState.isScreenReaderEnabled) {
      AccessibilityInfo.announceForAccessibility(message);
    }
  };

  const setAccessibilityFocus = (reactTag: number) => {
    if (accessibilityState.isScreenReaderEnabled) {
      AccessibilityInfo.setAccessibilityFocus(reactTag);
    }
  };

  const getRecommendedTimeout = async (originalTimeout: number) => {
    try {
      return await AccessibilityInfo.getRecommendedTimeoutMillis(originalTimeout);
    } catch {
      return originalTimeout;
    }
  };

  // Get accessible styles based on user preferences
  const getAccessibleStyles = () => {
    const styles: any = {};

    if (accessibilityState.isBoldTextEnabled) {
      styles.fontWeight = 'bold';
    }

    if (accessibilityState.isReduceTransparencyEnabled) {
      styles.opacity = 1;
    }

    if (accessibilityState.colorScheme === 'dark') {
      styles.backgroundColor = '#000000';
      styles.color = '#ffffff';
    }

    return styles;
  };

  // Get font scale based on accessibility settings
  const getFontScale = () => {
    // This would typically come from AccessibilityInfo.getRecommendedTimeoutMillis
    // but we'll provide a reasonable default scaling
    const category = accessibilityState.preferredContentSizeCategory;
    const scales: Record<string, number> = {
      'extra-small': 0.8,
      'small': 0.9,
      'medium': 1.0,
      'large': 1.1,
      'extra-large': 1.2,
      'extra-extra-large': 1.3,
      'extra-extra-extra-large': 1.4,
    };
    return scales[category] || 1.0;
  };

  // Check if high contrast is needed
  const needsHighContrast = () => {
    return accessibilityState.isInvertColorsEnabled || 
           accessibilityState.isGrayscaleEnabled ||
           accessibilityState.colorScheme === 'dark';
  };

  return {
    ...accessibilityState,
    announceForScreenReader,
    setAccessibilityFocus,
    getRecommendedTimeout,
    getAccessibleStyles,
    getFontScale,
    needsHighContrast,
  };
};

// Hook for accessible colors
export const useAccessibleColors = () => {
  const { colorScheme, needsHighContrast } = useAccessibility();

  const getColors = () => {
    const isDark = colorScheme === 'dark';
    const highContrast = needsHighContrast();

    return {
      // Background colors
      background: isDark ? '#000000' : '#ffffff',
      surface: isDark ? '#1F1F1F' : '#F9FAFB',
      
      // Text colors
      text: {
        primary: isDark ? '#ffffff' : '#1F2937',
        secondary: isDark ? '#D1D5DB' : '#6B7280',
        disabled: isDark ? '#6B7280' : '#9CA3AF',
      },
      
      // Interactive colors
      primary: highContrast ? (isDark ? '#60A5FA' : '#1D4ED8') : '#3B82F6',
      secondary: highContrast ? (isDark ? '#FBBF24' : '#D97706') : '#F59E0B',
      success: highContrast ? (isDark ? '#34D399' : '#059669') : '#10B981',
      error: highContrast ? (isDark ? '#F87171' : '#DC2626') : '#EF4444',
      warning: highContrast ? (isDark ? '#FBBF24' : '#D97706') : '#F59E0B',
      
      // Border colors
      border: {
        light: isDark ? '#374151' : '#E5E7EB',
        medium: isDark ? '#4B5563' : '#D1D5DB',
        strong: isDark ? '#6B7280' : '#9CA3AF',
      },
    };
  };

  return getColors();
};

// Hook for accessible font sizes
export const useAccessibleFontSizes = () => {
  const { getFontScale } = useAccessibility();
  const scale = getFontScale();

  return {
    xs: Math.round(10 * scale),
    sm: Math.round(12 * scale),
    base: Math.round(14 * scale),
    lg: Math.round(16 * scale),
    xl: Math.round(18 * scale),
    '2xl': Math.round(20 * scale),
    '3xl': Math.round(24 * scale),
    '4xl': Math.round(28 * scale),
    '5xl': Math.round(32 * scale),
  };
};

// Accessibility helper functions
export const createAccessibleTouchable = (
  label: string,
  hint?: string,
  role: 'button' | 'link' | 'tab' = 'button'
) => ({
  accessible: true,
  accessibilityRole: role,
  accessibilityLabel: label,
  accessibilityHint: hint,
});

export const createAccessibleText = (
  text: string,
  role: 'header' | 'text' | 'summary' = 'text'
) => ({
  accessible: true,
  accessibilityRole: role,
  accessibilityLabel: text,
});

export const createAccessibleInput = (
  label: string,
  value?: string,
  placeholder?: string,
  required?: boolean
) => ({
  accessible: true,
  accessibilityRole: 'text' as const,
  accessibilityLabel: label + (required ? ' (required)' : ''),
  accessibilityValue: value ? { text: value } : undefined,
  accessibilityHint: placeholder,
});
