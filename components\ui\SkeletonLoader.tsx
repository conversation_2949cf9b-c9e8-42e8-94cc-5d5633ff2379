import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, ViewStyle } from 'react-native';

interface SkeletonLoaderProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  animated?: boolean;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
  animated = true,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (!animated) return;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue, animated]);

  const backgroundColor = animated
    ? animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: ['#E5E7EB', '#F3F4F6'],
      })
    : '#E5E7EB';

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

// Skeleton components for specific UI elements
export const QuestionSkeleton: React.FC = () => (
  <View style={styles.questionSkeletonContainer}>
    {/* Category badge skeleton */}
    <SkeletonLoader width={80} height={24} borderRadius={12} style={styles.categoryBadgeSkeleton} />
    
    {/* Question text skeleton */}
    <SkeletonLoader width="90%" height={32} borderRadius={6} style={styles.questionTitleSkeleton} />
    <SkeletonLoader width="70%" height={32} borderRadius={6} style={styles.questionSubtitleSkeleton} />
  </View>
);

export const AnswersSkeleton: React.FC = () => (
  <View style={styles.answersSkeletonContainer}>
    {[1, 2, 3, 4].map((index) => (
      <View key={index} style={styles.answerSkeletonItem}>
        <SkeletonLoader width={32} height={32} borderRadius={16} />
        <SkeletonLoader width="80%" height={20} borderRadius={4} />
      </View>
    ))}
  </View>
);

export const ProgressSkeleton: React.FC = () => (
  <View style={styles.progressSkeletonContainer}>
    <SkeletonLoader width={60} height={16} borderRadius={4} style={styles.progressTextSkeleton} />
    <SkeletonLoader width="100%" height={4} borderRadius={2} style={styles.progressBarSkeleton} />
  </View>
);

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: '#E5E7EB',
  },
  questionSkeletonContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
  },
  categoryBadgeSkeleton: {
    marginBottom: 16,
  },
  questionTitleSkeleton: {
    marginBottom: 8,
  },
  questionSubtitleSkeleton: {
    marginBottom: 0,
  },
  answersSkeletonContainer: {
    paddingHorizontal: 24,
    gap: 16,
    paddingBottom: 32,
  },
  answerSkeletonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    gap: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
  },
  progressSkeletonContainer: {
    alignItems: 'center',
    paddingHorizontal: 16,
    gap: 8,
  },
  progressTextSkeleton: {
    marginBottom: 4,
  },
  progressBarSkeleton: {
    marginBottom: 4,
  },
});

// Complete assessment skeleton screen
export const AssessmentSkeleton: React.FC = () => (
  <View style={styles.skeletonContainer}>
    {/* Header skeleton */}
    <View style={styles.headerSkeleton}>
      <SkeletonLoader width={40} height={40} borderRadius={20} />
      <ProgressSkeleton />
      <SkeletonLoader width={40} height={40} borderRadius={20} />
    </View>
    
    {/* Question skeleton */}
    <QuestionSkeleton />
    
    {/* Answers skeleton */}
    <AnswersSkeleton />
    
    {/* Footer skeleton */}
    <View style={styles.footerSkeleton}>
      <SkeletonLoader width="100%" height={52} borderRadius={12} />
    </View>
  </View>
);

const skeletonStyles = StyleSheet.create({
  skeletonContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  footerSkeleton: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});

// Merge styles
Object.assign(styles, skeletonStyles);
