import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import {
  Bell,
  X,
  Check,
  Clock,
  Heart,
  Lightbulb,
  MessageSquare,
  Settings,
} from 'lucide-react-native';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'reminder' | 'insight' | 'achievement' | 'system';
  read: boolean;
  created_at: string;
  action?: {
    label: string;
    route?: string;
  };
}

interface NotificationSystemProps {
  onNavigate?: (route: string) => void;
}

export default function NotificationSystem({
  onNavigate,
}: NotificationSystemProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const subscriptionRef = useRef<any>(null);
  const isSubscribedRef = useRef(false);

  useEffect(() => {
    let mounted = true;

    const initializeNotifications = async () => {
      if (!mounted) return;

      await loadNotifications();

      // Only set up subscription if Supabase is configured and we have a user
      if (!isSupabaseConfigured()) {
        console.log(
          'Supabase not configured, skipping notifications subscription'
        );
        return;
      }

      // Prevent multiple subscriptions
      if (isSubscribedRef.current) {
        console.log('Notifications subscription already exists, skipping');
        return;
      }

      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user || !mounted) return;

        // Create a unique channel name to avoid conflicts
        const channelName = `notifications_${user.id}_${Date.now()}`;

        console.log('Setting up notifications subscription:', channelName);

        subscriptionRef.current = supabase
          .channel(channelName)
          .on(
            'postgres_changes',
            { event: '*', schema: 'public', table: 'user_notifications' },
            () => {
              if (mounted) {
                loadNotifications();
              }
            }
          )
          .subscribe();

        isSubscribedRef.current = true;
        console.log('Notifications subscription established');
      } catch (error) {
        console.warn('Failed to set up notifications subscription:', error);
      }
    };

    initializeNotifications();

    return () => {
      mounted = false;
      if (subscriptionRef.current) {
        console.log('Cleaning up notifications subscription');
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
        isSubscribedRef.current = false;
      }
    };
  }, []);

  const loadNotifications = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      // For now, we'll use mock notifications since we don't have a notifications table
      // In a real implementation, you would fetch from a user_notifications table
      const mockNotifications: Notification[] = [
        {
          id: '1',
          title: 'Daily Reflection Reminder',
          message:
            'Take 5 minutes tonight to reflect on 3 positive things that happened today',
          type: 'reminder',
          read: false,
          created_at: new Date().toISOString(),
          action: {
            label: 'Start Reflection',
            route: '/(tabs)/talk',
          },
        },
        {
          id: '2',
          title: 'New Personality Insight',
          message:
            "Based on your temperament, here's a tip for better communication today",
          type: 'insight',
          read: false,
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          action: {
            label: 'View Insight',
          },
        },
        {
          id: '3',
          title: 'Assessment Complete!',
          message:
            "Congratulations! You've completed your personality assessment",
          type: 'achievement',
          read: true,
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        },
      ];

      setNotifications(mockNotifications);
      const unread = mockNotifications.filter((n) => !n.read).length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      // Update local state immediately for better UX
      setNotifications((prev) =>
        prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n))
      );

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - 1));

      // In a real implementation, you would update the database here
      // await supabase
      //   .from('user_notifications')
      //   .update({ read: true })
      //   .eq('id', notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
      setUnreadCount(0);

      // In a real implementation, you would update the database here
      // const { data: { user } } = await supabase.auth.getUser();
      // if (user) {
      //   await supabase
      //     .from('user_notifications')
      //     .update({ read: true })
      //     .eq('user_id', user.id)
      //     .eq('read', false);
      // }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const sendTestNotification = async () => {
    if (Platform.OS === 'web') {
      // For web, we'll use the browser's notification API
      if ('Notification' in window) {
        if (Notification.permission === 'granted') {
          new Notification('Harmona', {
            body: 'This is a test notification from Harmona!',
            icon: '/favicon.png',
            badge: '/favicon.png',
          });
        } else if (Notification.permission !== 'denied') {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            new Notification('Harmona', {
              body: "Notifications enabled! You'll receive daily insights and reminders.",
              icon: '/favicon.png',
              badge: '/favicon.png',
            });
          }
        } else {
          Alert.alert(
            'Notifications Blocked',
            'Please enable notifications in your browser settings to receive daily insights and reminders.'
          );
        }
      } else {
        Alert.alert(
          'Notifications Not Supported',
          "Your browser doesn't support notifications."
        );
      }
    } else {
      // For mobile, you would use expo-notifications
      Alert.alert(
        'Test Notification',
        'This would send a push notification on mobile devices. For now, check the notification center in the app.'
      );
    }

    // Add a new test notification to the list
    const testNotification: Notification = {
      id: Date.now().toString(),
      title: 'Test Notification',
      message: 'This is a test notification to demonstrate the system!',
      type: 'system',
      read: false,
      created_at: new Date().toISOString(),
    };

    setNotifications((prev) => [testNotification, ...prev]);
    setUnreadCount((prev) => prev + 1);
  };

  const handleNotificationPress = (notification: Notification) => {
    markAsRead(notification.id);

    if (notification.action?.route && onNavigate) {
      onNavigate(notification.action.route);
      setShowModal(false);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'reminder':
        return <Clock size={20} color="#F59E0B" />;
      case 'insight':
        return <Lightbulb size={20} color="#3B82F6" />;
      case 'achievement':
        return <Heart size={20} color="#EF4444" />;
      case 'system':
        return <Settings size={20} color="#6B7280" />;
      default:
        return <Bell size={20} color="#6B7280" />;
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return diffInHours + 'h ago';
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return diffInDays + 'd ago';
    }
  };

  return (
    <>
      {/* Notification Bell Button */}
      <TouchableOpacity
        style={styles.bellButton}
        onPress={() => setShowModal(true)}
        activeOpacity={0.7}
      >
        <Bell size={20} color="#3B82F6" />
        {unreadCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>
              {unreadCount > 9 ? '9+' : unreadCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Notifications Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Notifications</Text>
              <View style={styles.headerActions}>
                {unreadCount > 0 && (
                  <TouchableOpacity
                    style={styles.markAllButton}
                    onPress={markAllAsRead}
                  >
                    <Check size={16} color="#3B82F6" />
                    <Text style={styles.markAllText}>Mark all read</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowModal(false)}
                >
                  <X size={24} color="#6B7280" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Test Notification Button */}
            <View style={styles.testSection}>
              <TouchableOpacity
                style={styles.testButton}
                onPress={sendTestNotification}
              >
                <Bell size={16} color="#3B82F6" />
                <Text style={styles.testButtonText}>
                  Send Test Notification
                </Text>
              </TouchableOpacity>
            </View>

            {/* Notifications List */}
            <ScrollView
              style={styles.notificationsList}
              showsVerticalScrollIndicator={false}
            >
              {notifications.length === 0 ? (
                <View style={styles.emptyState}>
                  <Bell size={48} color="#D1D5DB" />
                  <Text style={styles.emptyTitle}>No notifications yet</Text>
                  <Text style={styles.emptyText}>
                    You'll receive daily insights, reminders, and updates here
                  </Text>
                </View>
              ) : (
                notifications.map((notification) => (
                  <TouchableOpacity
                    key={notification.id}
                    style={[
                      styles.notificationItem,
                      !notification.read && styles.unreadNotification,
                    ]}
                    onPress={() => handleNotificationPress(notification)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.notificationIcon}>
                      {getNotificationIcon(notification.type)}
                    </View>

                    <View style={styles.notificationContent}>
                      <View style={styles.notificationHeader}>
                        <Text
                          style={[
                            styles.notificationTitle,
                            !notification.read && styles.unreadTitle,
                          ]}
                        >
                          {notification.title}
                        </Text>
                        <Text style={styles.notificationTime}>
                          {formatTime(notification.created_at)}
                        </Text>
                      </View>

                      <Text style={styles.notificationMessage}>
                        {notification.message}
                      </Text>

                      {notification.action && (
                        <Text style={styles.notificationAction}>
                          {notification.action.label} →
                        </Text>
                      )}
                    </View>

                    {!notification.read && <View style={styles.unreadDot} />}
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>

            {/* Footer */}
            <View style={styles.modalFooter}>
              <Text style={styles.footerText}>
                Manage notification preferences in Settings
              </Text>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  bellButton: {
    position: 'relative',
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#EBF8FF',
  },
  badge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  markAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#EBF8FF',
  },
  markAllText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  testSection: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#EBF8FF',
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  testButtonText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '600',
  },
  notificationsList: {
    maxHeight: 400,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    gap: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
  },
  emptyText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  unreadNotification: {
    backgroundColor: '#EBF8FF',
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    flex: 1,
    marginRight: 8,
  },
  unreadTitle: {
    fontWeight: '600',
    color: '#1E40AF',
  },
  notificationTime: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationAction: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  unreadDot: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3B82F6',
  },
  modalFooter: {
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
  },
});
