import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { <PERSON><PERSON><PERSON>, Clock, Wifi } from 'lucide-react-native';
import { useAccessibility } from '@/hooks/useAccessibility';
import { AssessmentSkeleton } from './SkeletonLoader';

interface EnhancedLoadingProps {
  type: 'initial' | 'generating' | 'submitting';
  useAI?: boolean;
  isRetake?: boolean;
  estimatedTime?: number; // in seconds
  showSkeleton?: boolean;
  message?: string;
}

export const EnhancedLoading: React.FC<EnhancedLoadingProps> = ({
  type,
  useAI = false,
  isRetake = false,
  estimatedTime,
  showSkeleton = false,
  message,
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [animatedValue] = useState(new Animated.Value(0));
  const { isReduceMotionEnabled, announceForScreenReader } = useAccessibility();

  useEffect(() => {
    // Start animation
    if (!isReduceMotionEnabled) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }

    // Track elapsed time
    const timer = setInterval(() => {
      setElapsedTime((prev) => prev + 1);
    }, 1000);

    // Announce loading state for screen readers
    const loadingMessage = getLoadingMessage();
    announceForScreenReader(loadingMessage);

    return () => {
      clearInterval(timer);
    };
  }, [animatedValue, isReduceMotionEnabled, announceForScreenReader]);

  const getLoadingMessage = () => {
    if (message) return message;

    switch (type) {
      case 'initial':
        return isRetake
          ? 'Preparing fresh questions for retake'
          : 'Loading assessment...';
      case 'generating':
        return useAI ? 'Generating fresh AI questions' : 'Loading questions';
      case 'submitting':
        return isRetake
          ? 'Updating your profile...'
          : 'Submitting assessment...';
      default:
        return 'Loading...';
    }
  };

  const getDetailMessage = () => {
    switch (type) {
      case 'initial':
        return 'Setting up your personalized assessment experience';
      case 'generating':
        return useAI
          ? 'Creating unique questions with mixed answer positions...'
          : 'Preparing your assessment questions...';
      case 'submitting':
        return 'Saving your responses and calculating results...';
      default:
        return 'Please wait while we process your request';
    }
  };

  const getIcon = () => {
    const iconProps = { size: 48, color: '#3B82F6' };

    switch (type) {
      case 'generating':
        return <Sparkles {...iconProps} />;
      case 'submitting':
        return <Wifi {...iconProps} />;
      default:
        return <Clock {...iconProps} />;
    }
  };

  const formatTime = (seconds: number) => {
    const roundedSeconds = Math.round(seconds);
    if (roundedSeconds < 60) {
      return roundedSeconds + 's';
    }
    const minutes = Math.floor(roundedSeconds / 60);
    const remainingSeconds = roundedSeconds % 60;
    return minutes + 'm ' + remainingSeconds + 's';
  };

  const getProgressEstimate = () => {
    if (!estimatedTime) return null;

    const progress = Math.min(
      Math.round((elapsedTime / estimatedTime) * 100),
      95
    );
    const remaining = Math.max(estimatedTime - elapsedTime, 0);

    return { progress, remaining };
  };

  if (showSkeleton && type === 'initial') {
    return <AssessmentSkeleton />;
  }

  const progressEstimate = getProgressEstimate();

  return (
    <View
      style={styles.container}
      accessible={true}
      accessibilityRole="progressbar"
      accessibilityLabel={'Loading: ' + getLoadingMessage()}
      accessibilityValue={
        progressEstimate
          ? { min: 0, max: 100, now: progressEstimate.progress }
          : undefined
      }
    >
      <View style={styles.content}>
        {/* Animated icon */}
        <Animated.View
          style={[
            styles.iconContainer,
            !isReduceMotionEnabled && {
              opacity: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.6, 1],
              }),
              transform: [
                {
                  scale: animatedValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.95, 1.05],
                  }),
                },
              ],
            },
          ]}
        >
          {getIcon()}
        </Animated.View>

        {/* Loading title */}
        <Text style={styles.title}>{getLoadingMessage()}</Text>

        {/* Detail message */}
        <Text style={styles.detail}>{getDetailMessage()}</Text>

        {/* Progress estimate */}
        {progressEstimate && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: progressEstimate.progress + '%' },
                ]}
              />
            </View>
            <View style={styles.timeInfo}>
              <Text style={styles.timeText}>
                {formatTime(elapsedTime)} elapsed
              </Text>
              {progressEstimate.remaining > 0 && (
                <Text style={styles.timeText}>
                  ~{formatTime(progressEstimate.remaining)} remaining
                </Text>
              )}
            </View>
          </View>
        )}

        {/* Special notices */}
        {isRetake && (
          <View style={styles.notice}>
            <Text style={styles.noticeText}>
              This will replace your previous results
            </Text>
          </View>
        )}

        {useAI && type === 'generating' && (
          <View style={styles.aiNotice}>
            <Sparkles size={16} color="#3B82F6" />
            <Text style={styles.aiNoticeText}>
              AI-powered questions for better accuracy
            </Text>
          </View>
        )}

        {/* Fallback message for long waits */}
        {elapsedTime > 30 && (
          <View style={styles.fallbackNotice}>
            <Text style={styles.fallbackText}>
              Taking longer than expected? This might be due to network
              conditions.
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 32,
    maxWidth: 400,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  detail: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  progressContainer: {
    width: '100%',
    marginBottom: 24,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  notice: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 16,
  },
  noticeText: {
    fontSize: 14,
    color: '#92400E',
    textAlign: 'center',
    fontWeight: '500',
  },
  aiNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 16,
    gap: 8,
  },
  aiNoticeText: {
    fontSize: 14,
    color: '#1E40AF',
    fontWeight: '500',
  },
  fallbackNotice: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
    borderLeftWidth: 4,
    borderLeftColor: '#F59E0B',
  },
  fallbackText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});
