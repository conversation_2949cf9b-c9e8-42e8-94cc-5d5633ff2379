import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  <PERSON>Lef<PERSON>,
  <PERSON>R<PERSON>,
  <PERSON>rk<PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  Triangle<PERSON>lert as Al<PERSON><PERSON>riangle,
} from 'lucide-react-native';
import { useAssessmentStore } from '@/stores';
import { openRouterService } from '@/lib/openrouter';
import { TEMPERAMENT_MAPPING } from '@/constants/assessment';
import { EnhancedLoading } from '@/components/ui/EnhancedLoading';
import { EnhancedProgress } from '@/components/ui/EnhancedProgress';
import { ErrorRecovery } from '@/components/ui/ErrorRecovery';
import {
  useAccessibility,
  createAccessibleTouchable,
} from '@/hooks/useAccessibility';

export default function AssessmentScreen() {
  // Get assessment store state and actions
  const {
    questions,
    currentQuestionIndex,
    selectedAnswer,
    phase,
    isLoading,
    isGeneratingQuestions,
    isSubmitting,
    error,
    aiError,
    isRetake,
    useAI,

    // Assessment store actions
    initializeAssessment,
    handleAnswerSelect,
    handleNextQuestion,
    handlePreviousQuestion,
    regenerateQuestions,
    useFallbackQuestions,
  } = useAssessmentStore();

  // Accessibility hooks
  const { announceForScreenReader, isScreenReaderEnabled } = useAccessibility();

  const mounted = useRef(true);
  const currentQ = useMemo(
    () => questions[currentQuestionIndex],
    [questions, currentQuestionIndex]
  );
  const isFirstQuestion = useMemo(
    () => currentQuestionIndex === 0,
    [currentQuestionIndex]
  );
  const isLastQuestion = useMemo(
    () => currentQuestionIndex === questions.length - 1,
    [currentQuestionIndex, questions.length]
  );
  const canNavigateBack = useMemo(() => !isFirstQuestion, [isFirstQuestion]);
  const canProceed = useMemo(
    () => selectedAnswer !== null && !isSubmitting,
    [selectedAnswer, isSubmitting]
  );

  // Memoized handlers to prevent unnecessary re-renders
  const handlePrevious = useCallback(() => {
    if (canNavigateBack) {
      handlePreviousQuestion();
    }
  }, [canNavigateBack, handlePreviousQuestion]);

  const handleNext = useCallback(() => {
    if (canProceed) {
      handleNextQuestion();
    }
  }, [canProceed, handleNextQuestion]);

  const handleAnswerSelection = useCallback(
    (answerIndex: number) => {
      handleAnswerSelect(answerIndex);
    },
    [handleAnswerSelect]
  );

  const handleRegenerate = useCallback(() => {
    if (!isGeneratingQuestions) {
      regenerateQuestions();
    }
  }, [isGeneratingQuestions, regenerateQuestions]);

  useEffect(() => {
    mounted.current = true;
    initializeAssessment();

    return () => {
      mounted.current = false;
    };
  }, [initializeAssessment]);

  // Announce question changes for screen readers
  useEffect(() => {
    if (currentQ && isScreenReaderEnabled) {
      const announcement = `Question ${currentQuestionIndex + 1} of ${
        questions.length
      }. ${currentQ.category} phase. ${currentQ.question}`;
      announceForScreenReader(announcement);
    }
  }, [
    currentQuestionIndex,
    currentQ,
    questions.length,
    isScreenReaderEnabled,
    announceForScreenReader,
  ]);

  // Announce answer selection for screen readers
  useEffect(() => {
    if (selectedAnswer !== null && currentQ && isScreenReaderEnabled) {
      const selectedText = currentQ.answers[selectedAnswer];
      const announcement = `Selected option ${String.fromCharCode(
        65 + selectedAnswer
      )}: ${selectedText}`;
      announceForScreenReader(announcement);
    }
  }, [
    selectedAnswer,
    currentQ,
    isScreenReaderEnabled,
    announceForScreenReader,
  ]);

  // Announce phase transitions
  useEffect(() => {
    if (isScreenReaderEnabled) {
      let announcement = '';
      switch (phase) {
        case 'confirmation':
          announcement =
            'Entering confirmation phase. These questions will help confirm your temperament.';
          break;
        case 'comparison':
          announcement =
            'Entering final comparison phase. These questions will determine your final results.';
          break;
        case 'complete':
          announcement =
            'Assessment complete! Calculating your personality profile.';
          break;
      }
      if (announcement) {
        announceForScreenReader(announcement);
      }
    }
  }, [phase, isScreenReaderEnabled, announceForScreenReader]);

  // Show loading state
  if (isLoading) {
    return (
      <EnhancedLoading
        type="initial"
        useAI={useAI}
        isRetake={isRetake}
        estimatedTime={15}
        showSkeleton={true}
      />
    );
  }

  // Show question generation state
  if (isGeneratingQuestions) {
    return (
      <EnhancedLoading
        type="generating"
        useAI={useAI}
        isRetake={isRetake}
        estimatedTime={useAI ? 25 : 10}
      />
    );
  }

  // Show error state
  if (error && !questions.length) {
    return (
      <SafeAreaView style={styles.container}>
        <ErrorRecovery
          error={error}
          onRetry={() => initializeAssessment()}
          onFallback={useFallbackQuestions}
          showDetails={true}
        />
      </SafeAreaView>
    );
  }

  if (!questions.length) {
    return (
      <SafeAreaView style={styles.container}>
        <ErrorRecovery
          error={{
            type: 'validation',
            message: 'No questions available',
            retryable: true,
            timestamp: new Date().toISOString(),
          }}
          onRetry={() => initializeAssessment()}
          onFallback={useFallbackQuestions}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={handlePrevious}
          disabled={!canNavigateBack}
          style={[
            styles.navButton,
            !canNavigateBack && styles.navButtonDisabled,
          ]}
          {...createAccessibleTouchable(
            'Previous question',
            canNavigateBack
              ? 'Go back to the previous question'
              : 'This is the first question'
          )}
        >
          <ArrowLeft
            size={24}
            color={!canNavigateBack ? '#9CA3AF' : '#1F2937'}
          />
        </TouchableOpacity>

        <View style={styles.progressContainer}>
          <EnhancedProgress
            currentStep={currentQuestionIndex + 1}
            totalSteps={questions.length}
            phase={phase}
            estimatedTimeRemaining={Math.max(
              0,
              (questions.length - currentQuestionIndex - 1) * 30
            )}
            showTimeEstimate={true}
          />
          {openRouterService.isConfigured() && (
            <TouchableOpacity
              style={styles.regenerateButton}
              onPress={handleRegenerate}
              disabled={isGeneratingQuestions}
              {...createAccessibleTouchable(
                'Generate fresh questions',
                'This will create new AI-generated questions for better variety'
              )}
            >
              <RefreshCw size={16} color="#3B82F6" />
              <Text style={styles.regenerateText}>Fresh Questions</Text>
            </TouchableOpacity>
          )}
          <View style={styles.indicatorContainer}>
            {isRetake && (
              <View style={styles.retakeIndicator}>
                <RefreshCw size={12} color="#F59E0B" />
                <Text style={styles.retakeIndicatorText}>Retake</Text>
              </View>
            )}
            {useAI && !aiError && openRouterService.isConfigured() && (
              <View style={styles.aiIndicator}>
                <Sparkles size={12} color="#3B82F6" />
                <Text style={styles.aiText}>AI-Generated</Text>
              </View>
            )}
            {aiError && (
              <View style={styles.fallbackIndicator}>
                <AlertTriangle size={12} color="#F59E0B" />
                <Text style={styles.fallbackText}>Standard Questions</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.navButton} />
      </View>

      {isRetake && (
        <View style={styles.retakeBanner}>
          <RefreshCw size={16} color="#F59E0B" />
          <Text style={styles.retakeBannerText}>
            Retaking assessment with fresh questions - this will replace your
            previous personality profile
          </Text>
        </View>
      )}

      {aiError && (
        <View style={styles.aiErrorBanner}>
          <AlertTriangle size={16} color="#F59E0B" />
          <Text style={styles.aiErrorText}>
            AI generation failed: {aiError}. Using enhanced standard questions.
          </Text>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.questionContainer}>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>
              {currentQ.category.charAt(0).toUpperCase() +
                currentQ.category.slice(1)}
            </Text>
          </View>

          <Text style={styles.question}>{currentQ.question}</Text>

          {currentQ.temperamentOrder && __DEV__ && (
            <Text style={styles.debugText}>
              Debug:{' '}
              {phase === 'comparison'
                ? /* Comparison phase has only 3 temperaments */
                  `A=${TEMPERAMENT_MAPPING[currentQ.temperamentOrder[0]]}, B=${
                    TEMPERAMENT_MAPPING[currentQ.temperamentOrder[1]]
                  }, C=${TEMPERAMENT_MAPPING[currentQ.temperamentOrder[2]]}`
                : /* Initial and confirmation phases have all 4 temperaments */
                  `A=${TEMPERAMENT_MAPPING[currentQ.temperamentOrder[0]]}, B=${
                    TEMPERAMENT_MAPPING[currentQ.temperamentOrder[1]]
                  }, C=${
                    TEMPERAMENT_MAPPING[currentQ.temperamentOrder[2]]
                  }, D=${TEMPERAMENT_MAPPING[currentQ.temperamentOrder[3]]}`}
            </Text>
          )}
        </View>

        <View style={styles.answersContainer}>
          {/* In comparison phase, we only show 3 answers instead of 4 */}
          {currentQ.answers.map((answer, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.answerButton,
                selectedAnswer === index && styles.selectedAnswer,
              ]}
              onPress={() => handleAnswerSelection(index)}
              {...createAccessibleTouchable(
                `Option ${String.fromCharCode(65 + index)}: ${answer}`,
                selectedAnswer === index
                  ? 'Currently selected answer'
                  : 'Tap to select this answer'
              )}
            >
              <View style={styles.answerContent}>
                <View
                  style={[
                    styles.answerIndicator,
                    selectedAnswer === index && styles.selectedIndicator,
                  ]}
                >
                  <Text
                    style={[
                      styles.answerLetter,
                      selectedAnswer === index && styles.selectedAnswerLetter,
                    ]}
                  >
                    {String.fromCharCode(65 + index)}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.answerText,
                    selectedAnswer === index && styles.selectedAnswerText,
                  ]}
                >
                  {answer}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.nextButton, !canProceed && styles.nextButtonDisabled]}
          onPress={handleNext}
          disabled={!canProceed}
          {...createAccessibleTouchable(
            isSubmitting
              ? isRetake
                ? 'Updating Profile...'
                : 'Submitting...'
              : isLastQuestion
              ? isRetake
                ? 'Update Assessment'
                : 'Complete Assessment'
              : 'Next Question',
            canProceed
              ? isLastQuestion
                ? 'Complete the assessment and see your results'
                : 'Continue to the next question'
              : 'Please select an answer to continue'
          )}
        >
          <Text style={styles.nextButtonText}>
            {isSubmitting
              ? isRetake
                ? 'Updating Profile...'
                : 'Submitting...'
              : isLastQuestion
              ? isRetake
                ? 'Update Assessment'
                : 'Complete Assessment'
              : 'Next Question'}
          </Text>
          {!isSubmitting && <ArrowRight size={20} color="#ffffff" />}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 24,
  },
  loadingTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginTop: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  retakeText: {
    fontSize: 14,
    color: '#F59E0B',
    textAlign: 'center',
    fontWeight: '500',
  },
  loader: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    gap: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#EF4444',
    textAlign: 'center',
    marginTop: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  errorButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  fallbackButton: {
    backgroundColor: '#F59E0B',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  fallbackButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  navButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButtonDisabled: {
    opacity: 0.3,
  },
  progressContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
  },
  regenerateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#EBF8FF',
  },
  regenerateText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '500',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  indicatorContainer: {
    minHeight: 16,
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  retakeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  retakeIndicatorText: {
    fontSize: 10,
    color: '#F59E0B',
    fontWeight: '500',
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  aiText: {
    fontSize: 10,
    color: '#3B82F6',
    fontWeight: '500',
  },
  fallbackIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  fallbackText: {
    fontSize: 10,
    color: '#F59E0B',
    fontWeight: '500',
  },
  retakeBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  retakeBannerText: {
    flex: 1,
    fontSize: 14,
    color: '#92400E',
    fontWeight: '500',
  },
  aiErrorBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  aiErrorText: {
    flex: 1,
    fontSize: 14,
    color: '#92400E',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  questionContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  categoryBadge: {
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 16,
  },
  categoryText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  question: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    lineHeight: 32,
  },
  debugText: {
    fontSize: 10,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 8,
    fontFamily: 'monospace',
  },
  answersContainer: {
    gap: 16,
    paddingBottom: 32,
  },
  answerButton: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedAnswer: {
    backgroundColor: '#EBF8FF',
    borderColor: '#3B82F6',
  },
  answerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    gap: 16,
  },
  answerIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIndicator: {
    backgroundColor: '#3B82F6',
  },
  answerLetter: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  selectedAnswerLetter: {
    color: '#ffffff',
  },
  answerText: {
    flex: 1,
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  selectedAnswerText: {
    color: '#1E40AF',
    fontWeight: '500',
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  nextButton: {
    backgroundColor: '#3B82F6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
